import React from 'react';
import { 
  FaHandPaper, 
  FaLightbulb, 
  FaRocket, 
  FaMobileAlt, 
  FaFolderOpen, 
  FaWrench, 
  FaPalette, 
  FaLock, 
  FaUsers, 
  FaPhone, 
  FaHospital, 
  FaHeart,
  FaHome,
  FaQrcode,
  FaUserPlus,
  FaChartBar,
  FaStethoscope,
  FaCalendarAlt,
  FaShieldAlt,
  FaTools,
  FaCog,
  FaInfoCircle,
  FaCheckCircle,
  FaExclamationTriangle,
  FaTimesCircle
} from 'react-icons/fa';

import {
  HiOutlineSparkles,
  HiOutlineLightBulb,
  HiOutlineHand
} from 'react-icons/hi';

import {
  IoMdHeart,
  IoMdHome,
  IoMdPeople
} from 'react-icons/io';

import {
  MdHealthAndSafety,
  MdLocalHospital,
  MdFavorite
} from 'react-icons/md';

// Interface untuk props icon
interface IconProps {
  className?: string;
  size?: number;
}

// Komponen icon yang dapat digunakan sebagai pengganti emoji
export const Icons = {
  // Gesture & Emotion Icons
  WavingHand: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaHandPaper className={className} size={size} />
  ),
  
  // Utility Icons
  Lightbulb: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaLightbulb className={className} size={size} />
  ),
  
  // Technology Icons
  Rocket: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaRocket className={className} size={size} />
  ),
  
  Mobile: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaMobileAlt className={className} size={size} />
  ),
  
  QRCode: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaQrcode className={className} size={size} />
  ),
  
  // File & Data Icons
  FileFolder: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaFolderOpen className={className} size={size} />
  ),
  
  // Tools Icons
  Wrench: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaWrench className={className} size={size} />
  ),
  
  Tools: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaTools className={className} size={size} />
  ),
  
  Settings: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaCog className={className} size={size} />
  ),
  
  // Design Icons
  Palette: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaPalette className={className} size={size} />
  ),
  
  // Security Icons
  Lock: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaLock className={className} size={size} />
  ),
  
  Shield: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaShieldAlt className={className} size={size} />
  ),
  
  // People Icons
  Users: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaUsers className={className} size={size} />
  ),
  
  UserPlus: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaUserPlus className={className} size={size} />
  ),
  
  // Communication Icons
  Phone: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaPhone className={className} size={size} />
  ),
  
  // Health & Medical Icons
  Hospital: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaHospital className={className} size={size} />
  ),
  
  Heart: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaHeart className={className} size={size} />
  ),
  
  HealthAndSafety: ({ className = "w-6 h-6", size }: IconProps) => (
    <MdHealthAndSafety className={className} size={size} />
  ),
  
  Stethoscope: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaStethoscope className={className} size={size} />
  ),
  
  // Navigation Icons
  Home: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaHome className={className} size={size} />
  ),
  
  // Analytics Icons
  ChartBar: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaChartBar className={className} size={size} />
  ),
  
  // Time Icons
  Calendar: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaCalendarAlt className={className} size={size} />
  ),
  
  // Status Icons
  Info: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaInfoCircle className={className} size={size} />
  ),
  
  Success: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaCheckCircle className={className} size={size} />
  ),
  
  Warning: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaExclamationTriangle className={className} size={size} />
  ),
  
  Error: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaTimesCircle className={className} size={size} />
  ),
  
  // Special Effects Icons
  Sparkles: ({ className = "w-6 h-6", size }: IconProps) => (
    <HiOutlineSparkles className={className} size={size} />
  )
};

// Export individual icons untuk kemudahan import
export const {
  WavingHand,
  Lightbulb,
  Rocket,
  Mobile,
  QRCode,
  FileFolder,
  Wrench,
  Tools,
  Settings,
  Palette,
  Lock,
  Shield,
  Users,
  UserPlus,
  Phone,
  Hospital,
  Heart,
  HealthAndSafety,
  Stethoscope,
  Home,
  ChartBar,
  Calendar,
  Info,
  Success,
  Warning,
  Error,
  Sparkles
} = Icons;

export default Icons;
