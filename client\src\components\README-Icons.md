# Icons Component Documentation

## Deskripsi
Komponen Icons menyediakan set icon yang konsisten menggunakan React Icons library sebagai pengganti emoji manual. Ini memastikan tampilan yang seragam di semua browser dan platform.

## Instalasi
```bash
pnpm add react-icons
```

## Import
```tsx
// Import semua icons
import Icons from '@/components/Icons';

// Import specific icons
import { WavingHand, Lightbulb, Hospital } from '@/components/Icons';
```

## Penggunaan

### Basic Usage
```tsx
// Menggunakan default props
<Icons.WavingHand />

// Dengan custom className
<Icons.Lightbulb className="w-8 h-8 text-blue-600" />

// Dengan size prop
<Icons.Hospital size={24} />
```

### Available Icons

#### Gesture & Emotion
- `WavingHand` - Menggantikan 👋
- `Heart` - Menggantikan ❤️

#### Technology
- `Rocket` - Menggantikan 🚀
- `Mobile` - Menggantikan 📱
- `QRCode` - Icon QR Code

#### Health & Medical
- `Hospital` - Menggantikan 🏥
- `Stethoscope` - Icon stetoskop
- `HealthAndSafety` - Icon kesehatan

#### Utility
- `Lightbulb` - Menggantikan 💡
- `Tools` - Menggantikan 🔧
- `Settings` - Icon pengaturan

#### File & Data
- `FileFolder` - Menggantikan 🗃️
- `ChartBar` - Icon grafik

#### Design
- `Palette` - Menggantikan 🎨
- `Sparkles` - Icon efek sparkle

#### Security
- `Lock` - Menggantikan 🔒
- `Shield` - Icon perisai

#### People
- `Users` - Menggantikan 👥
- `UserPlus` - Icon tambah user

#### Communication
- `Phone` - Menggantikan 📞

#### Navigation
- `Home` - Icon rumah

#### Status
- `Info` - Icon informasi
- `Success` - Icon sukses
- `Warning` - Icon peringatan
- `Error` - Icon error

#### Time
- `Calendar` - Icon kalender

## Props Interface
```tsx
interface IconProps {
  className?: string;  // Custom CSS classes
  size?: number;       // Icon size in pixels
}
```

## Contoh Implementasi

### Mengganti Emoji di Component
```tsx
// Sebelum (dengan emoji manual)
<span className="text-2xl">👋</span>

// Sesudah (dengan React Icons)
<WavingHand className="w-8 h-8 text-white" />
```

### Tips Section
```tsx
// Sebelum
<p>💡 Gunakan fitur Scan QR Code...</p>

// Sesudah
<div className="flex items-start space-x-2">
  <Lightbulb className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
  <p>Gunakan fitur Scan QR Code...</p>
</div>
```

## Keuntungan Menggunakan React Icons

1. **Konsistensi**: Tampilan sama di semua browser dan platform
2. **Customizable**: Mudah diubah warna, ukuran, dan style
3. **Performance**: Lebih ringan dari emoji Unicode
4. **Accessibility**: Lebih baik untuk screen readers
5. **Scalable**: Vector-based, tidak blur saat diperbesar
6. **Tree Shaking**: Hanya icon yang digunakan yang di-bundle

## Best Practices

1. **Gunakan semantic naming**: Pilih icon yang sesuai dengan konteks
2. **Konsisten sizing**: Gunakan ukuran yang konsisten dalam satu section
3. **Color harmony**: Sesuaikan warna icon dengan design system
4. **Accessibility**: Tambahkan aria-label jika diperlukan

```tsx
<Lightbulb 
  className="w-4 h-4 text-blue-600" 
  aria-label="Tips"
/>
```

## Migration Guide

### Emoji Manual → React Icons
- 👋 → `<WavingHand />`
- 💡 → `<Lightbulb />`
- 🚀 → `<Rocket />`
- 📱 → `<Mobile />`
- 🗃️ → `<FileFolder />`
- 🔧 → `<Tools />`
- 🎨 → `<Palette />`
- 🔒 → `<Lock />`
- 👥 → `<Users />`
- 📞 → `<Phone />`
- 🏥 → `<Hospital />`
- ❤️ → `<Heart />`

## Troubleshooting

### Icon tidak muncul
- Pastikan React Icons sudah terinstall
- Check import path yang benar
- Verifikasi className tidak override display

### Icon terlalu kecil/besar
- Gunakan prop `size` atau `className` dengan ukuran yang sesuai
- Untuk responsive, gunakan Tailwind responsive classes

### Warna tidak sesuai
- Icon inherit warna dari parent atau gunakan `text-{color}` classes
- Pastikan tidak ada CSS yang override warna icon
